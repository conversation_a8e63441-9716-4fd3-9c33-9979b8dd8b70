import {TextinOcrMapper} from "../../mapper/TextinOcrMapper";
import {toYearDateDirName} from "../time-util";
import {parseImagesInMarkdown} from "../markdown-util";

/**
 * 文档转为markdown格式
 */
export class DocToMarkdownConvertor {
    constructor(ctx, data) {
        this.ctx = ctx;
        this.data = data;

        // 文档在磁盘上的路径
        this.docPath = data.docPath;
        // 附件配置
        this.attachment = {
            // 附件保存的目录路径
            saveDirPath: data.attachment.saveDirPath,
            // 在文档中替换父级节点地址
            replacementParentUrl: data.attachment.replacementParentUrl,
        };
        // 类型
        this._type = data.type ?? 'mammoth';
    }

    async convert() {
        const { fs, mammoth } = this.ctx.imports;
        let markdown;

        if (this._type === 'textin') {
            const textinOcrMapper = new TextinOcrMapper(this.ctx, {});
            markdown = await textinOcrMapper
                .convertDocToMd(this.docPath, {
                    attachment: {
                        ...this.attachment,
                    }
                });
        }
        else if (this._type === 'mammoth') {
            const result = await mammoth.convertToMarkdown({ path: this.docPath })
            markdown = result.value;

            const images = parseImagesInMarkdown(markdown);
            log(`共解析出图片${images.length}条`)

            for (const image of images) {

            }
        }

        return markdown;
    }

    /**
     *
     * @param markdown
     * @param images {[name, content, ext, rawMatchText]} markdown中过滤出来的图片；content就是base64内容不是DataUrl；ext是图片格式，例：.jpg / .png；
     *  * rawMatchText是正则原始匹配到的内容，用于替换markdown中的文本
     * @param saveDirPath 存放图片文件的目录，例：./public/data/xxx
     * @param replacementParentUrl
     * @private
     */
    _saveImageBase64ContentToFileAndReplace(markdown, images, saveDirPath, replacementParentUrl) {
    }
}
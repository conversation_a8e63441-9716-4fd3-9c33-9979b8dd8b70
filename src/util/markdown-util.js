
/**
 * 清理markdown中的base64图片
 * @param {string} markdownContent markdown内容
 * @returns {string} 清理后的markdown内容
 */
export function clearImageInMarkdown(markdownContent) {
    if (!markdownContent || typeof markdownContent !== 'string') {
        return markdownContent;
    }

    // 移除包含base64数据的图片标记
    // 匹配格式: ![alt text](data:image/...;base64,...)
    const base64ImageRegex = /!\[.*?\]\(data:image\/[^;]+;base64,[^)]+\)/g;

    // 移除这些图片标记
    let cleanedContent = markdownContent.replace(base64ImageRegex, '');

    // 同时移除可能的多余空行
    cleanedContent = cleanedContent.replace(/\n{3,}/g, '\n\n');

    return cleanedContent.trim();
}

/**
 * 解析markdown中的所有图片
 * @param markdownContent
 * @returns {Array} 例：[{ name, content, ext, rawMatchText }] content就是base64内容不是DataUrl；ext是图片格式，例：.jpg / .png；
 * rawMatchText是正则原始匹配到的内容，用于替换markdown中的文本
 */
export function parseImagesInMarkdown(markdownContent) {
    if (!markdownContent || typeof markdownContent !== 'string') {
        return [];
    }

    const images = [];

    // 匹配格式: ![alt text](data:image/...;base64,...)
    const base64ImageRegex = /!\[([^\]]*)\]\(data:image\/([^;]+);base64,([^)]+)\)/g;

    let match;
    let imageIndex = 1;

    while ((match = base64ImageRegex.exec(markdownContent)) !== null) {
        const [rawMatchText, altText, imageType, base64Content] = match;

        // 生成图片名称，优先使用alt text，否则使用默认名称
        const imageName = altText.trim() || `image_${imageIndex}`;

        // 确定文件扩展名
        let ext = `.${imageType}`;
        // 处理一些常见的MIME类型映射
        if (imageType === 'jpeg') {
            ext = '.jpg';
        }

        images.push({
            name: imageName,
            content: base64Content, // 纯base64内容，不包含data:image前缀
            ext: ext,
            rawMatchText,
        });

        imageIndex++;
    }

    return images;
}